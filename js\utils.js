function convertImageToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
}
function removeDuplicateKeywords(keywordString) {
    if (!keywordString) return "";
    const keywordArray = keywordString.split(',').map(k => k.trim());
    const uniqueKeywords = new Set();
    const result = [];
    for (const keyword of keywordArray) {
        if (keyword.length < 2) continue;
        let cleanKeyword = keyword.replace(/\.$/, '');
        const lowerKeyword = cleanKeyword.toLowerCase();
        if (!uniqueKeywords.has(lowerKeyword)) {
            uniqueKeywords.add(lowerKeyword);
            result.push(lowerKeyword);
        }
    }
    return result.join(", ");
}
function parseGeminiResponse(text) {
    console.log("Raw response text:", text);
    text = text.replace(/\*\*/g, "");
    text = text.replace(/^and\s+keywords\s+following\s+your\s+(?:guidelines|rules|instructions):/i, "");
    text = text.replace(/^(here is|here are|and keywords|following your rules|following all the rules|as requested).*?:/i, "");
    let description = "";
    let keywords = "";
    if (text.includes("Description:") && text.includes("Keywords:")) {
        const descriptionMatch = text.match(/Description:(.*?)(?:Keywords:|Kata kunci:|Tags:)/si);
        const keywordsMatch = text.match(/(?:Keywords:|Kata kunci:|Tags:)(.*)/si);
        if (descriptionMatch) description = descriptionMatch[1].trim();
        if (keywordsMatch) keywords = keywordsMatch[1].trim();
    }
    else if (text.includes("Deskripsi:") && (text.includes("Kata kunci:") || text.includes("Keywords:"))) {
        const descriptionMatch = text.match(/Deskripsi:(.*?)(?:Kata kunci:|Keywords:|Tags:)/si);
        const keywordsMatch = text.match(/(?:Kata kunci:|Keywords:|Tags:)(.*)/si);
        if (descriptionMatch) description = descriptionMatch[1].trim();
        if (keywordsMatch) keywords = keywordsMatch[1].trim();
    }
    else {
        const lines = text.split(/\n+/);
        if (lines.length > 1) {
            const keywordLineIndex = lines.findIndex(line =>
                line.toLowerCase().includes("keywords:") ||
                line.toLowerCase().includes("kata kunci:") ||
                line.toLowerCase().includes("tags:"));
            if (keywordLineIndex !== -1) {
                description = lines.slice(0, keywordLineIndex).join(" ").trim();
                const keywordText = lines.slice(keywordLineIndex).join(" ");
                const keywordMatch = keywordText.match(/(?:keywords:|kata kunci:|tags:)(.*)/si);
                if (keywordMatch) {
                    keywords = keywordMatch[1].trim();
                } else {
                    keywords = keywordText.trim();
                }
            } else {
                description = lines[0].trim();
                keywords = lines.slice(1).join(", ").trim();
                keywords = keywords.replace(/(?:keywords:|kata kunci:|tags:)/gi, "").trim();
            }
        } else {
            description = text.trim();
        }
    }
    description = description.replace(/(?:description:|deskripsi:)/gi, "").trim();
    keywords = keywords.replace(/(?:keywords:|kata kunci:|tags:)/gi, "").trim();
    keywords = keywords.replace(/^:+|:+$/g, "").trim();
    keywords = removeDuplicateKeywords(keywords);
    description = description.replace(/^(here is|here are|and keywords|following your rules|following all the rules|as requested).*?:/i, "").trim();
    if (description.match(/^.*?description:/i)) {
        description = description.replace(/^.*?description:/i, "").trim();
    }
    return {
        description: description,
        keywords: keywords
    };
}
function cleanDescriptionText(description) {
    if (!description) return "";
    const original = description;
    let cleanedText = description;
    const specificPatterns = [
        { regex: /^(?:tailored to your specifications|created as requested|as per your request|according to your requirements).*?:/i, replace: "" },
        { regex: /^and\s+keywords(?:\s*,)?\s*(?:following\s+(?:all\s+)?your\s+(?:rules|guidelines|instructions|requirements))?:?/i, replace: "" },
        { regex: /^and\s+(?:set\s+of\s+)?keywords\s+(?:for\s+the\s+image)?(?:\s*,)?\s*(?:following\s+your\s+(?:rules|guidelines|instructions))?:?/i, replace: "" },
        { regex: /^description:?\s*/i, replace: "" },
        { regex: /(?:keywords:).*$/gis, replace: "" },
        { regex: /^(?:here is|here are|following your rules|following all the rules|as requested).*?:/i, replace: "" },
        { regex: /^(?:keywords:|tags:).*$/gim, replace: "" },
        { regex: /^(?:I've|I have)\s+created\s+(?:a|an|the)?\s+(?:description|image description).*?:/i, replace: "" },
        { regex: /^(?:As requested|As per your request),?\s+(?:here is|here are|I've provided|I have provided).*?:/i, replace: "" },
    ];
    specificPatterns.forEach(pattern => {
        cleanedText = cleanedText.replace(pattern.regex, pattern.replace);
    });
    const colonPrefixMatch = cleanedText.match(/^([^.!?]{5,50}?):\s*/);
    if (colonPrefixMatch) {
        const prefix = colonPrefixMatch[1].toLowerCase();
        const metaWords = ['description', 'keyword', 'following', 'requested', 'instruction', 'guideline', 'rule', 'specification'];
        const containsMetaWord = metaWords.some(word => prefix.includes(word));
        if (containsMetaWord) {
            cleanedText = cleanedText.replace(/^[^.!?]{5,50}?:\s*/, '');
        }
    }
    const metaInstructionPattern = /^([^.!?]*(?:following|according to|based on|as per)(?:[^.!?]*(?:instruction|guideline|rule|specification|requirement))[^.!?]*[.!?])\s*/i;
    const metaMatch = cleanedText.match(metaInstructionPattern);
    if (metaMatch) {
        cleanedText = cleanedText.replace(metaInstructionPattern, '');
    }
    cleanedText = cleanedText.replace(/(?:description:|keywords:|tags:)/gi, "");
    cleanedText = cleanedText.replace(/\n{3,}/g, "\n\n");
    cleanedText = cleanedText.trim();
    if (cleanedText === "" && original !== "") {
        cleanedText = original.replace(/^(?:description:|keywords:)/i, "").trim();
        if (cleanedText === "") {
            const firstSentence = original.split(/[.!?]/)[0];
            if (firstSentence) {
                return firstSentence.trim() + ".";
            }
            return original;
        }
    }
    return cleanedText;
}

function getStatusHTML(status) {
    status = status || "Pending";
    if (status === "Completed") {
        return `<span class="badge bg-success"><i class="bi bi-check-circle-fill me-1"></i> Completed</span>`;
    } else if (status === "Processing" || status === "Processing...") {
        return `<span class="badge bg-primary"><div class="spinner-border spinner-border-sm me-1" role="status"><span class="visually-hidden">Loading...</span></div> Processing</span>`;
    } else if (status === "Queued") {
        return `<span class="badge bg-info text-dark"><i class="bi bi-hourglass-split me-1"></i> Queued</span>`;
    } else if (status === "Stopped") {
        return `<span class="badge bg-warning text-dark"><i class="bi bi-stop-fill me-1"></i> Stopped</span>`;
    } else if (status === "Paused") {
        return `<span class="badge bg-info text-dark"><i class="bi bi-pause-fill me-1"></i> Paused</span>`;
    } else if (status.startsWith("Retrying")) {
        return `<span class="badge bg-info"><div class="spinner-border spinner-border-sm me-1" role="status"><span class="visually-hidden">Retrying...</span></div> ${status}</span>`;
    } else if (status.startsWith("Error")) {
        const errorMessage = status.substring(6).trim();
        return `<span class="badge bg-danger" title="${errorMessage}"><i class="bi bi-exclamation-triangle-fill me-1"></i> Error</span>`;
    } else {
        return `<span class="badge bg-secondary"><i class="bi bi-hourglass me-1"></i> ${status}</span>`;
    }
}
function showConfirmModal(message, onConfirm, onCancel = null) {
    const existingModal = document.getElementById('confirmModal');
    if (existingModal) {
        existingModal.remove();
    }
    const modalHtml = `
        <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header" style="background-color: var(--light-purple); border-bottom: 1px solid var(--medium-purple);">
                        <h5 class="modal-title" id="confirmModalLabel" style="color: var(--dark-purple);">
                            <i class="bi bi-exclamation-circle-fill me-2"></i>Confirm
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-0">${message}</p>
                    </div>
                    <div class="modal-footer" style="border-top: 1px solid var(--medium-purple);">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" style="background-color: var(--primary-purple); border-color: var(--primary-purple);">
                            <i class="bi bi-check-circle-fill me-2"></i>Yes
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = document.getElementById('confirmModal');
    const modalInstance = new bootstrap.Modal(modal);
    const confirmButton = modal.querySelector('.btn-primary');
    confirmButton.addEventListener('click', () => {
        modalInstance.hide();
        if (onConfirm) onConfirm();
    });
    const cancelButton = modal.querySelector('.btn-secondary');
    cancelButton.addEventListener('click', () => {
        modalInstance.hide();
        if (onCancel) onCancel();
    });
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
    modalInstance.show();
}

function deleteRow(rowData) {
    if (!rowData || !rowData.name) {
        showToast("Invalid row data for deletion", "error");
        return;
    }
    showConfirmModal(
        `Are you sure you want to delete data for "${rowData.name}"?`,
        () => {
            const rowIndex = metadataResults.findIndex(item => item.name === rowData.name);
            if (rowIndex === -1) {
                showToast("Row not found in data source", "error");
                return;
            }
            const previewHTML = metadataResults[rowIndex].preview;
            const imgElement = document.createElement('div');
            imgElement.innerHTML = previewHTML;
            const imgSrc = imgElement.querySelector('img')?.src;
            if (imgSrc && imgSrc.startsWith('blob:')) {
                URL.revokeObjectURL(imgSrc);
            }
            metadataResults.splice(rowIndex, 1);
            const imageFileIndex = images.findIndex(img => img.name === rowData.name);
            if (imageFileIndex !== -1) {
                images.splice(imageFileIndex, 1);
            }
            if (table) {
                const row = table.getRow(rowData.name);
                if (row) {
                    row.delete()
                       .then(() => {
                           showToast("Row deleted successfully");
                           updateUIState();
                           updateExportButtonsState();
                       })
                       .catch(err => {
                            console.error("Error deleting row from table:", err);
                            showToast("Error deleting row from table.", "error");
                       });
                } else {
                     showToast("Row deleted successfully");
                     updateUIState();
                     updateExportButtonsState();
                }
            } else {
                showToast("Row deleted successfully");
                updateUIState();
                updateExportButtonsState();
            }
        }
    );
}
function setupContextMenuHoverBehavior() {
    document.body.addEventListener('mouseover', function (e) {
        const menuItem = e.target.closest('.tabulator-menu-item');
        if (menuItem && menuItem.querySelector('.tabulator-menu-item-submenu-marker')) {
            const submenu = menuItem.querySelector('.tabulator-menu');
            if (!submenu || submenu.style.display === 'none') {
            }
        }
    }, true);
    const styleId = 'tabulator-hover-styles';
    if (!document.getElementById(styleId)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .tabulator-menu {
                max-height: 77vh;
                overflow-y: auto;
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                border-radius: 4px;
                border: 1px solid #dee2e6;
                padding: 4px 0;
            }
            .tabulator-menu .tabulator-menu-item {
                padding: 2px 12px;
                font-size: 14px;
                transition: background-color 0.2s;
            }
            .tabulator-menu .tabulator-menu-item:hover {
                background-color: #e9ecef;
            }
            .tabulator-menu-item[role="separator"] {
                margin: 4px 0;
                border-top: 1px solid #dee2e6;
            }
            .tabulator-menu-item.menu-group-header {
                font-weight: 600;
                color: #495057;
                background-color: #f8f9fa;
                padding: 8px 12px;
                cursor: default;
                font-size: 0.85rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            .tabulator-menu-item.menu-group-header:hover {
                background-color: #f8f9fa;
            }
            .tabulator-menu::-webkit-scrollbar {
                width: 6px;
            }
            .tabulator-menu::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }
            .tabulator-menu::-webkit-scrollbar-thumb {
                background: #888;
                border-radius: 3px;
            }
            .tabulator-menu::-webkit-scrollbar-thumb:hover {
                background: #555;
            }
        `;
        document.head.appendChild(style);
    }
}
function stopProcessing() {
    if (!isProcessing) return;
    console.log("stopProcessing called.");
    showToast("Stopping processing...", "warning");
    shouldStopProcessing = true;
    isPaused = false;
    totalProcessingEndTime = Date.now();

    // Stop any active parallel processor
    if (window.parallelProcessor && typeof window.parallelProcessor.stopProcessing === 'function') {
        console.log("Signaling parallelProcessor to stop...");
        window.parallelProcessor.stopProcessing();
    } else {
        console.log("Parallel processor not found or stopProcessing not available.");
    }

    updateProcessingUI(false);

    // Update status for all pending/processing items
    let stoppedCount = 0;
    metadataResults.forEach((item, index) => {
        if (["Pending", "Processing", "Processing...", "Queued", "Paused"].includes(item.status) || item.status.startsWith("Retrying")) {
            item.status = "Stopped";
            stoppedCount++;
            updateTableWithMetadata(index, null, "Stopped");
        }
    });

    console.log(`Marked ${stoppedCount} items as Stopped in data source.`);
}
function finishProcessing() {
    console.log("finishProcessing called. isProcessing:", isProcessing, "shouldStopProcessing:", shouldStopProcessing);
    if (!isProcessing) {
        console.log("finishProcessing skipped as isProcessing is already false.");
        return;
    }
    isProcessing = false;
    isPaused = false;
    currentProcessingIndex = 0;
    totalProcessingEndTime = Date.now();
    updateProcessingUI(false);
    if (table) {
        try {
            console.log("finishProcessing: Updating table data with final statuses.");

            // Validate and clean data before updating
            const cleanedData = metadataResults.map(item => {
                return {
                    name: item.name || '',
                    preview: item.preview || '',
                    title: item.title || '',
                    description: item.description || '',
                    keywords: item.keywords || '',
                    shutterstockCategories: item.shutterstockCategories || '',
                    adobestockCategory: item.adobestockCategory || '',
                    status: item.status || 'Pending',
                    startTime: item.startTime || null,
                    processingTime: item.processingTime || 0,
                    isVideo: item.isVideo || false,
                    fromImagen: item.fromImagen || false
                };
            });

            // Use setData instead of updateData to avoid row finding issues
            table.setData(cleanedData)
                .then(() => {
                    console.log("Table data updated successfully in finishProcessing");
                    // Force a redraw to ensure UI is properly updated
                    table.redraw(true);
                })
                .catch(err => {
                    console.error("Error updating table in finishProcessing:", err);
                    // Fallback: try to update individual rows
                    console.log("Attempting individual row updates as fallback");
                    cleanedData.forEach((item, index) => {
                        try {
                            const row = table.getRow(item.name);
                            if (row) {
                                row.update(item);
                            }
                        } catch (rowError) {
                            console.warn(`Failed to update row ${item.name}:`, rowError);
                        }
                    });
                });
        } catch (error) {
            console.error("Error updating table in finishProcessing:", error);
        }
    }
    updateExportButtonsState();
    console.log("Processing finished and UI/state reset by finishProcessing.");
}


function pauseProcessing() {
    if (!isProcessing) return;
    const pauseBtn = document.getElementById('pauseBtn');
    if (!pauseBtn) return;

    if (isPaused) {
        // Resume processing
        isPaused = false;

        // Update UI
        pauseBtn.innerHTML = '<i class="bi bi-pause-fill"></i> Pause';
        pauseBtn.classList.remove('btn-success');
        pauseBtn.classList.add('btn-warning');

        // Resume parallel processor if available
        if (window.parallelProcessor && typeof window.parallelProcessor.resume === 'function') {
            console.log("Resuming parallel processor");
            window.parallelProcessor.resume();
        }

        // Update any paused items in the table
        metadataResults.forEach((item, index) => {
            if (item.status === "Paused") {
                updateTableWithMetadata(index, null, "Processing");
            }
        });

        showToast("Processing resumed", "info");
    } else {
        // Pause processing
        isPaused = true;

        // Update UI
        pauseBtn.innerHTML = '<i class="bi bi-play-fill"></i> Resume';
        pauseBtn.classList.remove('btn-warning');
        pauseBtn.classList.add('btn-success');

        // Pause parallel processor if available
        if (window.parallelProcessor && typeof window.parallelProcessor.pause === 'function') {
            console.log("Pausing parallel processor");
            window.parallelProcessor.pause();
        }

        // Update any processing items in the table
        metadataResults.forEach((item, index) => {
            if (item.status === "Processing") {
                updateTableWithMetadata(index, null, "Paused");
            }
        });

        showToast("Processing paused", "info");
    }
}