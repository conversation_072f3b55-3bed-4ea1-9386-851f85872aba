// Global state variables
let images = [];
let metadataResults = [];
let table;
let isProcessing = false;
let shouldStopProcessing = false;
let isPaused = false;
let currentProcessingIndex = 0;
let totalProcessingStartTime = null;
let totalProcessingEndTime = null;

// Global manager instances
const exportManager = new ExportManager();
const keywordManager = new KeywordManager();

// Global window bindings
window.exportToCSV = () => exportManager.exportToCSV();
window.addKeywordToPreview = () => keywordManager.addKeywordToPreview();
window.metadataResults = metadataResults;
window.isProcessing = isProcessing;
window.shouldStopProcessing = shouldStopProcessing;
window.isPaused = isPaused;
window.currentProcessingIndex = currentProcessingIndex;
window.totalProcessingStartTime = totalProcessingStartTime;
window.totalProcessingEndTime = totalProcessingEndTime;

// Web worker compatibility check and UI update functions removed as parallel processing now handles workers efficiently
async function generateMetadata() {
    if (isProcessing) {
        showToast("Processing is already in progress.", "warning");
        return;
    }
    if (images.length === 0) {
        showToast("No images selected to process.", "warning");
        return;
    }

    console.log("generateMetadata: Resetting state...");
    currentProcessingIndex = 0;
    shouldStopProcessing = false;
    isPaused = false;
    totalProcessingStartTime = Date.now();
    totalProcessingEndTime = null;

    if (window.parallelProcessor && typeof window.parallelProcessor.reset === 'function') {
        try {
            window.parallelProcessor.reset();
        } catch (error) {
            console.error("Error resetting parallel processor before generate:", error);
            showToast("Error preparing processor. Please try again.", "error");
            return;
        }
    } else {
         console.log("generateMetadata: Parallel processor not found or no reset method.");
    }

    isProcessing = true;
    updateProcessingUI(true);

    metadataResults.forEach(item => {
        if (item.status !== "Completed" && !item.status.startsWith("Error")) {
             item.status = 'Queued';
        }
    });
    if (table) table.setData(metadataResults);

    const enableParallel = localStorage.getItem('csvision_enable_parallel') === 'true';
    const concurrency = parseInt(localStorage.getItem('csvision_concurrency') || '1');
    let processingMode;
    let processingPromise;
    if (enableParallel) {
        processingMode = 'parallel';
    } else {
        processingMode = 'sequential';
    }
    console.log(`Starting processing in mode: ${processingMode}`);

    if (processingMode !== 'sequential') {
        if (!window.parallelProcessor || typeof window.parallelProcessor.processImages !== 'function') {
             window.parallelProcessor = new ParallelProcessor({
                 concurrency: concurrency,
                 requestsPerSecond: parseInt(localStorage.getItem('csvision_rate') || '5')
             });
             console.log("ParallelProcessor initialized.");
        } else {
             window.parallelProcessor.concurrency = concurrency;
             window.parallelProcessor.requestsPerSecond = parseInt(localStorage.getItem('csvision_rate') || '5');
             console.log("ParallelProcessor settings updated.");
        }
    }

    try {
        // Filter images to only process those with 'Queued' status
        const imagesToProcess = images.map((img, index) => ({
            image: img,
            index: index,
            status: metadataResults[index]?.status
        })).filter(item => item.status === "Queued");

        console.log(`generateMetadata: Processing ${imagesToProcess.length} images with 'Queued' status out of ${images.length} total images`);

        // Untuk mode paralel, atur status "Processing" untuk beberapa item pertama
        // sesuai dengan jumlah concurrency untuk memastikan timer berjalan dengan benar
        if (processingMode !== 'sequential') {
            const initialBatchSize = Math.min(concurrency, metadataResults.length);
            for (let i = 0; i < initialBatchSize; i++) {
                if (metadataResults[i].status === "Queued") {
                    updateTableWithMetadata(i, null, "Processing");
                }
            }
        }

        switch (processingMode) {
            case 'parallel':
                 if (!window.parallelProcessor || typeof window.parallelProcessor.processImages !== 'function') {
                     throw new Error("Parallel processor is not available.");
                 }
                 processingPromise = window.parallelProcessor.processImages(imagesToProcess, updateParallelProgress);
                 break;
            case 'sequential':
                 processingPromise = processImages();
                 break;
            default:
                 throw new Error(`Unknown processing mode: ${processingMode}`);
        }

        console.log("generateMetadata: Waiting for processing promise...");
        await processingPromise;
        console.log("generateMetadata: Processing promise resolved.");

        if (!shouldStopProcessing) {
            showToast(`Processing completed (${processingMode} mode).`, "success");
        } else {
            showToast("Processing stopped by user.", "warning");
        }
    } catch (error) {

        console.error(`Error during ${processingMode} processing:`, error);

        if (error.name !== 'AbortError' && !shouldStopProcessing) {
             showToast(`An error occurred during ${processingMode} processing. Check console for details.`, "error");
        } else if (shouldStopProcessing) {
             showToast("Processing stopped by user.", "warning");
        }
    } finally {

        console.log("generateMetadata: Finally block reached. isProcessing:", isProcessing);


        if (isProcessing) {
             finishProcessing();
        } else {
             console.log("generateMetadata: Skipping finishProcessing in finally block as isProcessing is false.");
        }
    }
}
async function processImage(image, index, retryCount = 0, maxRetries = 3) {
    if (index < 0 || index >= metadataResults.length) {
        console.error("Invalid index in processImage:", index);
        return;
    }

    const controller = new AbortController();
    const signal = controller.signal;

    // Jika sudah mencapai batas retry, tandai sebagai error dan lanjutkan
    if (retryCount > maxRetries) {
        console.error(`Maximum retries (${maxRetries}) reached for image ${index}`);
        updateTableWithMetadata(index, null, `Error: Maximum retries reached`);
        return;
    }

    try {
        updateTableWithMetadata(index, null, "Processing");

        const metadata = await callGeminiAPI(image, false, signal);
        if (shouldStopProcessing) {
             updateTableWithMetadata(index, null, "Cancelled");
             console.log(`Sequential processImage ${index} cancelled after API call.`);
             return;
        }
        updateTableWithMetadata(index, metadata, "Completed");
    } catch (error) {
        console.error(`Error processing image ${image.name} at index ${index}:`, error);
        if (error.name === 'AbortError' || shouldStopProcessing) {
            updateTableWithMetadata(index, null, "Cancelled");
            console.log(`Sequential processImage ${index} cancelled due to error/stop.`);
        } else {
            // Jika error dan belum mencapai batas retry, coba lagi setelah delay
            if (retryCount < maxRetries) {
                const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff
                console.log(`Retrying image ${index} (attempt ${retryCount + 1}) after ${retryDelay}ms...`);
                updateTableWithMetadata(index, null, `Retrying... (${retryCount + 1}/${maxRetries})`);

                // Tunggu sebentar sebelum mencoba lagi
                await new Promise(resolve => setTimeout(resolve, retryDelay));

                // Coba lagi dengan menambah retryCount
                return processImage(image, index, retryCount + 1, maxRetries);
            } else {
                // Jika sudah mencapai batas retry, tandai sebagai error
                updateTableWithMetadata(index, null, `Error: ${error.message || "Unknown error"} (after ${maxRetries} retries)`);
            }
        }
    }
}

async function processImages() {
    console.log("Sequential processImages started.");
    const maxRetries = 3; // Jumlah maksimum percobaan ulang
    let failedItems = []; // Array untuk menyimpan indeks gambar yang gagal

    // Proses semua gambar terlebih dahulu
    while (currentProcessingIndex < metadataResults.length && !shouldStopProcessing) {
        if (isPaused) {
            console.log("Sequential paused, waiting...");
            await new Promise(resolve => {
                const checkPause = () => {
                    if (!isPaused || shouldStopProcessing) {
                        console.log("Sequential resuming or stopping...");
                        resolve();
                    } else {
                        setTimeout(checkPause, 100);
                    }
                };
                checkPause();
            });
        }
        if (shouldStopProcessing) {
             console.log("Sequential detected stop flag after pause.");
             break;
        }
        if (isPaused) continue;
        const currentIndex = currentProcessingIndex;
        const currentItem = metadataResults[currentIndex];
        if (currentItem.status === "Completed") {
            currentProcessingIndex++;
            continue;
        }

        // Skip items that are not queued
        if (currentItem.status !== "Queued") {
            // If it's an error, add to failedItems for potential retry later
            if (currentItem.status.startsWith("Error")) {
                failedItems.push(currentIndex);
            }
            currentProcessingIndex++;
            continue;
        }

        console.log(`Sequential processing index: ${currentIndex}`);
        updateProcessingStatus(currentIndex, metadataResults.length);
        try {
            await processImage(images[currentIndex], currentIndex, 0, maxRetries);
        } catch (error) {
            console.error(`Error processing image ${currentIndex} sequentially:`, error);
            if (!shouldStopProcessing) {
                 updateTableWithMetadata(currentIndex, null, `Error: ${error.message || "Sequential processing failed"}`);
                 failedItems.push(currentIndex); // Tambahkan ke daftar failedItems
            }
        }
        if (shouldStopProcessing) {
             console.log("Sequential detected stop flag after processing item.");
             break;
        }
        currentProcessingIndex++;
        await new Promise(resolve => requestAnimationFrame(resolve));
    }

    // Jika ada gambar yang gagal dan tidak ada permintaan untuk berhenti, coba lagi
    if (failedItems.length > 0 && !shouldStopProcessing) {
        console.log(`Retrying ${failedItems.length} failed items...`);
        showToast(`Retrying ${failedItems.length} failed items...`, "info");

        // Coba lagi semua gambar yang gagal
        for (let i = 0; i < failedItems.length && !shouldStopProcessing; i++) {
            const index = failedItems[i];
            if (shouldStopProcessing) break;

            console.log(`Retrying failed item at index ${index}...`);
            updateProcessingStatus(index, metadataResults.length);
            try {
                // Reset status menjadi Queued sebelum mencoba lagi
                updateTableWithMetadata(index, null, "Queued");
                await processImage(images[index], index, 0, maxRetries);
            } catch (error) {
                console.error(`Error retrying image ${index}:`, error);
                if (!shouldStopProcessing) {
                    updateTableWithMetadata(index, null, `Error: ${error.message || "Retry failed"}`);
                }
            }
            await new Promise(resolve => requestAnimationFrame(resolve));
        }
    }

    console.log("Sequential processImages finished or stopped.");
}